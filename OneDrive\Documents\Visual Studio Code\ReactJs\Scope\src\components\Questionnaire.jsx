import { useState, useEffect } from 'react'
import questionsData from '../data/questions.json'

function Questionnaire({ onComplete, onBack, onProgressUpdate }) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [responses, setResponses] = useState([])
  const [selectedOption, setSelectedOption] = useState(null)
  const [selectedRating, setSelectedRating] = useState(null)
  const [questions, setQuestions] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load questions from JSON file with loading state
    setIsLoading(true)
    try {
      if (questionsData && questionsData.questions) {
        // Simulate loading time for smooth transition
        setTimeout(() => {
          setQuestions(questionsData.questions)
          setIsLoading(false)
          // Update progress in parent component
          if (onProgressUpdate) {
            onProgressUpdate({ current: 0, total: questionsData.questions.length })
          }
        }, 300)
      } else {
        console.error('Questions data not found or invalid format')
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Error loading questions:', error)
      setIsLoading(false)
    }
  }, [])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'Enter' && selectedOption && selectedRating) {
        handleNext()
      } else if (event.key === 'Escape') {
        setSelectedOption(null)
        setSelectedRating(null)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [selectedOption, selectedRating])

  const handleOptionRatingSelect = (option, rating) => {
    // If clicking the same option and rating, deselect it
    if (selectedOption === option && selectedRating === rating) {
      setSelectedOption(null)
      setSelectedRating(null)
    } else {
      setSelectedOption(option)
      setSelectedRating(rating)
    }
  }

  const handleNext = () => {
    if (selectedOption && selectedRating) {
      const currentQ = questions[currentQuestion]
      const selectedOptionData = selectedOption === 'A' ? currentQ.optionA : currentQ.optionB

      const newResponse = {
        questionId: currentQ.id,
        selectedOption: selectedOption,
        selectedCode: selectedOptionData.code,
        rating: selectedRating,
        questionText: currentQ.text,
        selectedText: selectedOptionData.text
      }

      const updatedResponses = [...responses, newResponse]
      setResponses(updatedResponses)

      if (currentQuestion < questions.length - 1) {
        const nextQuestion = currentQuestion + 1
        setCurrentQuestion(nextQuestion)
        setSelectedOption(null)
        setSelectedRating(null)
        // Update progress
        if (onProgressUpdate) {
          onProgressUpdate({ current: nextQuestion, total: questions.length })
        }
      } else {
        onComplete(updatedResponses)
      }
    }
  }

  const handleBack = () => {
    if (currentQuestion > 0) {
      const prevQuestion = currentQuestion - 1
      setCurrentQuestion(prevQuestion)
      setSelectedOption(null)
      setSelectedRating(null)
      // Remove last response
      setResponses(responses.slice(0, -1))
      // Update progress
      if (onProgressUpdate) {
        onProgressUpdate({ current: prevQuestion, total: questions.length })
      }
    } else {
      onBack()
    }
  }

  const progress = questions.length > 0 ? ((currentQuestion + 1) / questions.length) * 100 : 0

  // Helper function to get illustration for question option
  const getIllustration = (optionCode, optionText) => {
    const illustrations = {
      R: "🔧", // Realistic - tools, hands-on
      I: "🔬", // Investigative - research, analysis
      A: "🎨", // Artistic - creative, expressive
      S: "👥", // Social - people, helping
      E: "💼", // Enterprising - business, leadership
      C: "📊"  // Conventional - organized, systematic
    }
    return illustrations[optionCode] || "📋"
  }

  // Helper function to get emoji for rating
  const getRatingEmoji = (rating) => {
    const emojis = {
      1: "😐",
      2: "😊",
      3: "😍"
    }
    return emojis[rating] || "😐"
  }

  // Helper function to get rating label
  const getRatingLabel = (rating) => {
    const labels = {
      1: "Just ok",
      2: "Like",
      3: "Love it"
    }
    return labels[rating] || "Just ok"
  }

  // Show loading state if questions haven't loaded yet
  if (isLoading || questions.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading questions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto h-full flex flex-col">
      <div className="bg-white rounded-lg shadow-lg p-8 flex-1 flex flex-col">
        {/* Progress Bar with Diagonal Stripes */}
        <div className="mb-8">
          <div className="flex justify-end text-sm text-gray-600 mb-2">
            <span className="font-medium">{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
            <div
              className="h-4 rounded-full transition-all duration-500 bg-gradient-to-r from-blue-500 to-blue-600 relative"
              style={{ width: `${progress}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"></div>
              <div
                className="absolute inset-0 opacity-30"
                style={{
                  backgroundImage: 'repeating-linear-gradient(45deg, transparent, transparent 4px, rgba(255,255,255,0.3) 4px, rgba(255,255,255,0.3) 8px)'
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Question */}
        <div className="flex-1 flex flex-col">
          <div className="text-center mb-2">
            <span className="text-sm text-gray-500 font-medium">
              Question {currentQuestion + 1} of {questions.length}
            </span>
          </div>
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            {questions[currentQuestion].text}
          </h2>
          
          {/* Options Layout */}
          <div className="flex-1 flex flex-col lg:flex-row items-center justify-center gap-8 mb-8">
            {/* Option A */}
            <div className={`flex-1 max-w-md transition-all duration-300 ${
              selectedOption && selectedOption !== 'A'
                ? 'opacity-40 saturate-50 scale-95'
                : 'opacity-100 saturate-100 scale-100'
            }`}>
              <div className="text-center mb-6">
                <div className="text-6xl sm:text-8xl mb-4 transform transition-all duration-300 hover:scale-110">
                  {getIllustration(questions[currentQuestion].optionA.code, questions[currentQuestion].optionA.text)}
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-2">
                  {questions[currentQuestion].optionA.text}
                </h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {questions[currentQuestion].optionA.hint}
                </p>
              </div>

              {/* Rating Buttons for Option A */}
              <div className="flex flex-col gap-3">
                {[1, 2, 3].map((rating) => (
                  <button
                    key={`A-${rating}`}
                    onClick={() => handleOptionRatingSelect('A', rating)}
                    className={`flex items-center justify-center gap-3 p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 min-h-[60px] cursor-pointer ${
                      selectedOption === 'A' && selectedRating === rating
                        ? 'border-blue-500 bg-blue-50 transform scale-110 shadow-lg ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:scale-105 hover:shadow-md'
                    }`}
                  >
                    <span className="text-xl sm:text-2xl transform transition-all duration-200 hover:scale-125">
                      {getRatingEmoji(rating)}
                    </span>
                    <span className="font-medium text-gray-700 text-sm sm:text-base">
                      {getRatingLabel(rating)}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* OR Divider */}
            <div className="flex items-center justify-center lg:flex-col lg:h-full">
              <div className="hidden lg:block w-px bg-gray-300 flex-1"></div>
              <div className="bg-white px-4 py-2 rounded-full border-2 border-gray-300 text-gray-600 font-bold text-lg mx-4 lg:mx-0 lg:my-4">
                OR
              </div>
              <div className="hidden lg:block w-px bg-gray-300 flex-1"></div>
              <div className="lg:hidden flex-1 h-px bg-gray-300"></div>
            </div>

            {/* Option B */}
            <div className={`flex-1 max-w-md transition-all duration-300 ${
              selectedOption && selectedOption !== 'B'
                ? 'opacity-40 saturate-50 scale-95'
                : 'opacity-100 saturate-100 scale-100'
            }`}>
              <div className="text-center mb-6">
                <div className="text-6xl sm:text-8xl mb-4 transform transition-all duration-300 hover:scale-110">
                  {getIllustration(questions[currentQuestion].optionB.code, questions[currentQuestion].optionB.text)}
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-2">
                  {questions[currentQuestion].optionB.text}
                </h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {questions[currentQuestion].optionB.hint}
                </p>
              </div>

              {/* Rating Buttons for Option B */}
              <div className="flex flex-col gap-3">
                {[1, 2, 3].map((rating) => (
                  <button
                    key={`B-${rating}`}
                    onClick={() => handleOptionRatingSelect('B', rating)}
                    className={`flex items-center justify-center gap-3 p-3 sm:p-4 rounded-lg border-2 transition-all duration-200 min-h-[60px] cursor-pointer ${
                      selectedOption === 'B' && selectedRating === rating
                        ? 'border-blue-500 bg-blue-50 transform scale-110 shadow-lg ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:scale-105 hover:shadow-md'
                    }`}
                  >
                    <span className="text-xl sm:text-2xl transform transition-all duration-200 hover:scale-125">
                      {getRatingEmoji(rating)}
                    </span>
                    <span className="font-medium text-gray-700 text-sm sm:text-base">
                      {getRatingLabel(rating)}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 gap-4">
          {/* Back Button - Only show if not first question */}
          {currentQuestion > 0 ? (
            <button
              onClick={handleBack}
              className="flex items-center gap-2 px-4 sm:px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-all duration-200 transform hover:scale-105 min-h-[48px]"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="hidden sm:inline">Back</span>
            </button>
          ) : (
            <div></div>
          )}

          {/* Next Button - Only show if selection is made */}
          {selectedOption && selectedRating ? (
            <button
              onClick={handleNext}
              className="flex items-center gap-2 px-4 sm:px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg min-h-[48px] animate-fade-in"
            >
              <span className="text-sm sm:text-base">
                {currentQuestion < questions.length - 1 ? (
                  <>
                    <span className="hidden sm:inline">Next Question</span>
                    <span className="sm:hidden">Next</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Complete Assessment</span>
                    <span className="sm:hidden">Complete</span>
                  </>
                )}
              </span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          ) : (
            <div className="text-center text-gray-500 text-sm">
              <p>Select an option and rating to continue</p>
              <p className="text-xs mt-1">Press ESC to clear selection</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Questionnaire
