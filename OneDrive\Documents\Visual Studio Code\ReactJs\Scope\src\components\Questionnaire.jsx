import { useState } from 'react'

function Questionnaire({ onComplete, onBack }) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [responses, setResponses] = useState([])
  const [selectedOption, setSelectedOption] = useState(null)
  const [rating, setRating] = useState(null)

  // Placeholder questions - will load from JSON in next step
  const questions = [
    {
      id: 1,
      text: "Which task would you enjoy more?",
      optionA: { text: "Building a robot kit", code: "R", hint: "Hands-on construction" },
      optionB: { text: "Analyzing patterns in stock market data", code: "I", hint: "Data investigation" }
    }
  ]

  const handleOptionSelect = (option) => {
    setSelectedOption(option)
    setRating(null) // Reset rating when option changes
  }

  const handleRatingSelect = (ratingValue) => {
    setRating(ratingValue)
  }

  const handleNext = () => {
    if (selectedOption && rating) {
      const newResponse = {
        questionId: questions[currentQuestion].id,
        selectedOption: selectedOption,
        rating: rating
      }
      
      const updatedResponses = [...responses, newResponse]
      setResponses(updatedResponses)
      
      if (currentQuestion < questions.length - 1) {
        setCurrentQuestion(currentQuestion + 1)
        setSelectedOption(null)
        setRating(null)
      } else {
        onComplete(updatedResponses)
      }
    }
  }

  const handleBack = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
      setSelectedOption(null)
      setRating(null)
      // Remove last response
      setResponses(responses.slice(0, -1))
    } else {
      onBack()
    }
  }

  const progress = ((currentQuestion + 1) / questions.length) * 100

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Question {currentQuestion + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Question */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            {questions[currentQuestion].text}
          </h2>
          
          {/* Options */}
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <button
              onClick={() => handleOptionSelect('A')}
              className={`p-6 rounded-lg border-2 text-left transition-all duration-200 ${
                selectedOption === 'A' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-semibold text-gray-800 mb-2">
                {questions[currentQuestion].optionA.text}
              </div>
              <div className="text-sm text-gray-600">
                {questions[currentQuestion].optionA.hint}
              </div>
            </button>
            
            <button
              onClick={() => handleOptionSelect('B')}
              className={`p-6 rounded-lg border-2 text-left transition-all duration-200 ${
                selectedOption === 'B' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-semibold text-gray-800 mb-2">
                {questions[currentQuestion].optionB.text}
              </div>
              <div className="text-sm text-gray-600">
                {questions[currentQuestion].optionB.hint}
              </div>
            </button>
          </div>

          {/* Rating Scale */}
          {selectedOption && (
            <div className="mb-6">
              <p className="text-lg font-medium text-gray-700 mb-4">
                How much do you like this activity?
              </p>
              <div className="flex justify-center space-x-4">
                {[
                  { value: 1, label: 'Okay' },
                  { value: 2, label: 'Like' },
                  { value: 3, label: 'Love It' }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleRatingSelect(option.value)}
                    className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      rating === option.value
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <button
            onClick={handleBack}
            className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200"
          >
            {currentQuestion === 0 ? 'Back to Home' : 'Previous'}
          </button>
          
          <button
            onClick={handleNext}
            disabled={!selectedOption || !rating}
            className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
              selectedOption && rating
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {currentQuestion === questions.length - 1 ? 'Complete' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Questionnaire
