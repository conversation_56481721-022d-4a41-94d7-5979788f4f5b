import { useState, useEffect } from 'react'
import questionsData from '../data/questions.json'
import IllustrationCard from './IllustrationCard'

function Questionnaire({ onComplete, onBack, onProgressUpdate }) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [responses, setResponses] = useState([])
  const [selectedOption, setSelectedOption] = useState(null)
  const [selectedRating, setSelectedRating] = useState(null)
  const [questions, setQuestions] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load questions from JSON file with loading state
    setIsLoading(true)
    try {
      if (questionsData && questionsData.questions) {
        // Simulate loading time for smooth transition
        setTimeout(() => {
          setQuestions(questionsData.questions)
          setIsLoading(false)
          // Update progress in parent component
          if (onProgressUpdate) {
            onProgressUpdate({ current: 0, total: questionsData.questions.length })
          }
        }, 300)
      } else {
        console.error('Questions data not found or invalid format')
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Error loading questions:', error)
      setIsLoading(false)
    }
  }, [])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'Enter' && selectedOption && selectedRating) {
        handleNext()
      } else if (event.key === 'Escape') {
        setSelectedOption(null)
        setSelectedRating(null)
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [selectedOption, selectedRating])

  const handleOptionRatingSelect = (option, rating) => {
    // If clicking the same option and rating, deselect it
    if (selectedOption === option && selectedRating === rating) {
      setSelectedOption(null)
      setSelectedRating(null)
    } else {
      setSelectedOption(option)
      setSelectedRating(rating)
    }
  }

  const handleNext = () => {
    if (selectedOption && selectedRating) {
      const currentQ = questions[currentQuestion]
      const selectedOptionData = selectedOption === 'A' ? currentQ.optionA : currentQ.optionB

      const newResponse = {
        questionId: currentQ.id,
        selectedOption: selectedOption,
        selectedCode: selectedOptionData.code,
        rating: selectedRating,
        questionText: currentQ.text,
        selectedText: selectedOptionData.text
      }

      const updatedResponses = [...responses, newResponse]
      setResponses(updatedResponses)

      if (currentQuestion < questions.length - 1) {
        const nextQuestion = currentQuestion + 1
        setCurrentQuestion(nextQuestion)
        setSelectedOption(null)
        setSelectedRating(null)
        // Update progress
        if (onProgressUpdate) {
          onProgressUpdate({ current: nextQuestion, total: questions.length })
        }
      } else {
        onComplete(updatedResponses)
      }
    }
  }

  const handleBack = () => {
    if (currentQuestion > 0) {
      const prevQuestion = currentQuestion - 1
      setCurrentQuestion(prevQuestion)
      setSelectedOption(null)
      setSelectedRating(null)
      // Remove last response
      setResponses(responses.slice(0, -1))
      // Update progress
      if (onProgressUpdate) {
        onProgressUpdate({ current: prevQuestion, total: questions.length })
      }
    } else {
      onBack()
    }
  }

  const progress = questions.length > 0 ? ((currentQuestion + 1) / questions.length) * 100 : 0

  // Helper function to get illustration placeholder for question option
  const getIllustrationPlaceholder = (optionCode, optionText) => {
    const placeholders = {
      R: "Person working with tools/hands-on project", // Realistic - tools, hands-on
      I: "Person conducting research/analyzing data", // Investigative - research, analysis
      A: "Person creating art/being creative", // Artistic - creative, expressive
      S: "Person helping others/working with people", // Social - people, helping
      E: "Person in business meeting/leadership role", // Enterprising - business, leadership
      C: "Person organizing data/working systematically"  // Conventional - organized, systematic
    }

    // Create more specific descriptions based on option text
    const specificDescriptions = {
      "Working with your hands": "Person building/crafting something with tools",
      "Solving complex problems": "Person analyzing data on computer/whiteboard",
      "Creating something beautiful": "Person painting/designing artwork",
      "Helping people": "Person mentoring/teaching others",
      "Leading a team": "Person presenting to a group/team meeting",
      "Organizing information": "Person organizing files/data charts",
      "Fixing things": "Person repairing equipment/troubleshooting",
      "Researching": "Person in laboratory/reading research",
      "Performing": "Person on stage/performing arts",
      "Teaching": "Person in classroom/explaining concepts",
      "Starting a business": "Person with startup/entrepreneurial setting",
      "Following procedures": "Person following checklist/systematic work"
    }

    return specificDescriptions[optionText] || placeholders[optionCode] || "Person engaged in career activity"
  }

  // Helper function to get emoji for rating
  const getRatingEmoji = (rating) => {
    const emojis = {
      1: "😐",
      2: "😊",
      3: "😍"
    }
    return emojis[rating] || "😐"
  }

  // Helper function to get rating label
  const getRatingLabel = (rating) => {
    const labels = {
      1: "Just ok",
      2: "Like",
      3: "Love it"
    }
    return labels[rating] || "Just ok"
  }

  // Show loading state if questions haven't loaded yet
  if (isLoading || questions.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading questions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container-youth-full h-full flex flex-col animate-fade-in">
      <div className="card-youth flex-1 flex flex-col">
        {/* Enhanced Progress Bar with Youth-Friendly Design */}
        <div className="mb-8">
          <div className="flex justify-between items-center text-sm mb-3">
            <span className="font-heading font-medium text-neutral-600">
              Question {currentQuestion + 1} of {questions.length}
            </span>
            <span className="font-heading font-bold text-primary-600 bg-primary-50 px-3 py-1 rounded-full">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <div className="w-full bg-neutral-200 rounded-full h-5 overflow-hidden shadow-inner">
            <div
              className="h-5 rounded-full transition-all duration-700 ease-out bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-pink-400 relative animate-progress-fill"
              style={{
                width: `${progress}%`,
                '--progress-width': `${progress}%`
              }}
            >
              {/* Animated shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer"></div>
              {/* Diagonal stripes pattern */}
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: 'repeating-linear-gradient(45deg, transparent, transparent 6px, rgba(255,255,255,0.4) 6px, rgba(255,255,255,0.4) 12px)'
                }}
              ></div>
              {/* Celebration sparkles at milestones */}
              {progress >= 25 && progress < 30 && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-yellow-300 animate-bounce">
                  ✨
                </div>
              )}
              {progress >= 50 && progress < 55 && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-yellow-300 animate-bounce">
                  🎉
                </div>
              )}
              {progress >= 75 && progress < 80 && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-yellow-300 animate-bounce">
                  🚀
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Question Section */}
        <div className="flex-1 flex flex-col">
          <div className="text-center mb-6">
            <h2 className="text-title font-heading text-gradient-primary mb-4 animate-slide-in-left">
              {questions[currentQuestion].text}
            </h2>
            <p className="text-subtitle text-neutral-600 font-primary max-w-2xl mx-auto animate-slide-in-right">
              Choose the activity that appeals to you more and rate how much you'd enjoy it.
            </p>
          </div>

          {/* Options Layout - Mobile-First Design */}
          <div className="flex-1 flex flex-col lg:flex-row items-stretch justify-center gap-6 lg:gap-12 mb-8">
            {/* Option A */}
            <div className={`flex-1 max-w-md mx-auto lg:mx-0 transition-all duration-500 ease-out ${
              selectedOption && selectedOption !== 'A'
                ? 'opacity-60 saturate-75 scale-95'
                : 'opacity-100 saturate-100 scale-100'
            } ${selectedOption === 'A' ? 'animate-bounce-in' : ''}`}>
              <div className="text-center mb-6">
                <IllustrationCard
                  riasecCode={questions[currentQuestion].optionA.code}
                  optionText={questions[currentQuestion].optionA.text}
                  size="md"
                  variant="primary"
                  className="mx-auto"
                  isSelected={selectedOption === 'A'}
                  isDisabled={selectedOption && selectedOption !== 'A'}
                  animationDelay={100}
                />
                <h3 className="text-lg sm:text-xl font-heading font-semibold text-neutral-800 mb-2">
                  {questions[currentQuestion].optionA.text}
                </h3>
                <p className="text-neutral-600 text-sm sm:text-base font-primary leading-relaxed">
                  {questions[currentQuestion].optionA.hint}
                </p>
              </div>

              {/* Rating Buttons for Option A */}
              <div className="flex flex-col gap-3">
                {[1, 2, 3].map((rating) => (
                  <button
                    key={`A-${rating}`}
                    onClick={() => handleOptionRatingSelect('A', rating)}
                    className={`rating-btn touch-target flex items-center justify-center gap-4 p-4 sm:p-5 rounded-youth border-2 transition-all duration-300 min-h-[64px] font-heading ${
                      selectedOption === 'A' && selectedRating === rating
                        ? 'selected animate-bounce-in'
                        : 'hover:border-primary-400 hover:bg-primary-50 hover:scale-105 hover:shadow-youth'
                    }`}
                  >
                    <span className="text-2xl sm:text-3xl transform transition-all duration-300 hover:scale-125 animate-float" style={{ animationDelay: `${rating * 100}ms` }}>
                      {getRatingEmoji(rating)}
                    </span>
                    <span className="font-semibold text-neutral-700 text-base sm:text-lg">
                      {getRatingLabel(rating)}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* VS Divider - Youth-Friendly */}
            <div className="flex items-center justify-center lg:flex-col lg:h-full animate-pulse-gentle">
              <div className="hidden lg:block w-px bg-gradient-to-b from-primary-300 via-secondary-300 to-accent-pink-300 flex-1"></div>
              <div className="bg-gradient-youth text-white px-6 py-3 rounded-full font-accent text-lg shadow-youth mx-4 lg:mx-0 lg:my-6 transform hover:scale-110 transition-all duration-300">
                VS
              </div>
              <div className="hidden lg:block w-px bg-gradient-to-b from-accent-pink-300 via-secondary-300 to-primary-300 flex-1"></div>
              <div className="lg:hidden flex-1 h-px bg-gradient-to-r from-primary-300 via-secondary-300 to-accent-pink-300"></div>
            </div>

            {/* Option B */}
            <div className={`flex-1 max-w-md mx-auto lg:mx-0 transition-all duration-500 ease-out ${
              selectedOption && selectedOption !== 'B'
                ? 'opacity-60 saturate-75 scale-95'
                : 'opacity-100 saturate-100 scale-100'
            } ${selectedOption === 'B' ? 'animate-bounce-in' : ''}`}>
              <div className="text-center mb-6">
                <IllustrationCard
                  riasecCode={questions[currentQuestion].optionB.code}
                  optionText={questions[currentQuestion].optionB.text}
                  size="md"
                  variant="secondary"
                  className="mx-auto"
                  isSelected={selectedOption === 'B'}
                  isDisabled={selectedOption && selectedOption !== 'B'}
                  animationDelay={200}
                />
                <h3 className="text-lg sm:text-xl font-heading font-semibold text-neutral-800 mb-2">
                  {questions[currentQuestion].optionB.text}
                </h3>
                <p className="text-neutral-600 text-sm sm:text-base font-primary leading-relaxed">
                  {questions[currentQuestion].optionB.hint}
                </p>
              </div>

              {/* Rating Buttons for Option B */}
              <div className="flex flex-col gap-3">
                {[1, 2, 3].map((rating) => (
                  <button
                    key={`B-${rating}`}
                    onClick={() => handleOptionRatingSelect('B', rating)}
                    className={`rating-btn touch-target flex items-center justify-center gap-4 p-4 sm:p-5 rounded-youth border-2 transition-all duration-300 min-h-[64px] font-heading ${
                      selectedOption === 'B' && selectedRating === rating
                        ? 'selected animate-bounce-in'
                        : 'hover:border-secondary-400 hover:bg-secondary-50 hover:scale-105 hover:shadow-youth'
                    }`}
                  >
                    <span className="text-2xl sm:text-3xl transform transition-all duration-300 hover:scale-125 animate-float" style={{ animationDelay: `${rating * 100 + 300}ms` }}>
                      {getRatingEmoji(rating)}
                    </span>
                    <span className="font-semibold text-neutral-700 text-base sm:text-lg">
                      {getRatingLabel(rating)}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation - Youth-Friendly Design */}
        <div className="flex justify-between items-center mt-8 gap-4">
          {/* Back Button - Only show if not first question */}
          {currentQuestion > 0 ? (
            <button
              onClick={handleBack}
              className="btn-secondary flex items-center gap-3 touch-target interactive-scale"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="hidden sm:inline font-heading">Back</span>
            </button>
          ) : (
            <div></div>
          )}

          {/* Next Button - Only show if selection is made */}
          {selectedOption && selectedRating ? (
            <button
              onClick={handleNext}
              className="btn-primary flex items-center gap-3 touch-target interactive-scale animate-bounce-in"
            >
              <span className="font-heading text-base sm:text-lg">
                {currentQuestion < questions.length - 1 ? (
                  <>
                    <span className="hidden sm:inline">Next Question</span>
                    <span className="sm:hidden">Next</span>
                  </>
                ) : (
                  <>
                    <span className="hidden sm:inline">Complete Assessment 🎉</span>
                    <span className="sm:hidden">Complete 🎉</span>
                  </>
                )}
              </span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          ) : (
            <div className="text-center text-neutral-500 text-sm font-primary max-w-xs">
              <p className="mb-1">👆 Select an option and rating to continue</p>
              <p className="text-xs text-neutral-400">Press ESC to clear selection</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Questionnaire
