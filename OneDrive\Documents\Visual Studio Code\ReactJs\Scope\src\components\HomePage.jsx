function HomePage({ onStartQuestionnaire }) {
  return (
    <div className="max-w-4xl mx-auto text-center">
      <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          Welcome to SCOPE
        </h1>
        <p className="text-xl text-gray-600 mb-6">
          Career Assessment & Guidance System
        </p>
        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
          Discover your ideal career path through our scientifically-designed RIASEC assessment. 
          Answer 30 questions to uncover your personality type and get personalized course recommendations 
          for your college journey.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">Quick & Easy</h3>
            <p className="text-blue-600">Complete in under 15 minutes</p>
          </div>
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="font-semibold text-green-800 mb-2">Personalized</h3>
            <p className="text-green-600">Get tailored course recommendations</p>
          </div>
          <div className="bg-purple-50 p-6 rounded-lg">
            <h3 className="font-semibold text-purple-800 mb-2">Anonymous</h3>
            <p className="text-purple-600">No account required</p>
          </div>
        </div>
        
        <button
          onClick={onStartQuestionnaire}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
        >
          Start Assessment
        </button>
      </div>
      
      <div className="bg-gray-100 rounded-lg p-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
          What is RIASEC?
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-left">
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-red-600">Realistic (R)</h4>
            <p className="text-sm text-gray-600">Hands-on, practical work</p>
          </div>
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-blue-600">Investigative (I)</h4>
            <p className="text-sm text-gray-600">Research and analysis</p>
          </div>
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-purple-600">Artistic (A)</h4>
            <p className="text-sm text-gray-600">Creative expression</p>
          </div>
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-green-600">Social (S)</h4>
            <p className="text-sm text-gray-600">Helping and teaching</p>
          </div>
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-orange-600">Enterprising (E)</h4>
            <p className="text-sm text-gray-600">Leadership and business</p>
          </div>
          <div className="bg-white p-4 rounded">
            <h4 className="font-semibold text-gray-600">Conventional (C)</h4>
            <p className="text-sm text-gray-600">Organization and data</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage
