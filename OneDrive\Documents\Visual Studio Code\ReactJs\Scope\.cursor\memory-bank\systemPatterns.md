# System Patterns

## Architecture Overview
- Client-Server model
- Frontend: React SPA
- Backend: Node.js/Express API

## Design Patterns
- Container/Presentation for React components
- Layered architecture on server (routes → controllers → services)
- Event-driven email service

## Data Flow
1. User completes questionnaire
2. Frontend computes partial scores and submits to API
3. API calculates final RIASEC scores
4. Service sends email with results
5. Frontend displays basic summary
