/**
 * Filipino Cultural Content
 * 
 * Culturally relevant content, messaging, and data for Filipino youth
 * Includes motivational messages, career insights, and cultural references
 */

// Motivational messages in Filipino context
export const MOTIVATIONAL_MESSAGES = {
  start: [
    "Kaya mo yan! Your future starts now! 🌟",
    "<PERSON><PERSON> lang! Every great journey begins with a single step! 💪",
    "Believe in yourself! You have the power to achieve your dreams! ✨",
    "Tara na! Let's discover your amazing potential! 🚀",
    "You're destined for greatness! Simula na ng success story mo! 📖"
  ],
  progress: [
    "Galing mo! You're doing great! Keep going! 👏",
    "Halfway there na! Your determination is inspiring! 🎯",
    "Ang sipag mo! Your hard work will pay off! 💪",
    "Almost there! Your dreams are getting closer! ⭐",
    "Proud of you! You're making excellent progress! 🌟"
  ],
  completion: [
    "Congratulations! You did it! Napakagaling mo! 🎉",
    "Amazing! You've completed your journey to self-discovery! 🏆",
    "Wow! Your dedication paid off! Time to chase your dreams! 🚀",
    "Excellent work! You're ready to conquer the world! 🌍",
    "Fantastic! Your future is bright and full of possibilities! ✨"
  ],
  encouragement: [
    "Don't give up! Every Filipino hero faced challenges too! 💪",
    "<PERSON>a mo yan! You have the strength of your ancestors! 🇵🇭",
    "Believe in yourself! You're capable of amazing things! ⭐",
    "Keep going! Your family believes in you! ❤️",
    "Stay strong! Your dreams are worth fighting for! 🥊"
  ]
}

// Filipino career role models and success stories
export const FILIPINO_ROLE_MODELS = {
  realistic: [
    {
      name: "Engr. Aisa Mijeno",
      field: "Engineering",
      achievement: "Inventor of SALt lamp (Sustainable Alternative Lighting)",
      inspiration: "Turned seawater into light to help remote communities",
      quote: "Innovation should serve humanity, especially the underserved."
    },
    {
      name: "Dado Banatao",
      field: "Technology",
      achievement: "Silicon Valley pioneer, inventor of computer chips",
      inspiration: "From rice farmer's son to tech billionaire",
      quote: "Education and hard work can overcome any obstacle."
    }
  ],
  investigative: [
    {
      name: "Dr. Fe del Mundo",
      field: "Medicine",
      achievement: "First woman admitted to Harvard Medical School",
      inspiration: "Pioneered pediatric medicine in the Philippines",
      quote: "The best way to serve your country is to serve its children."
    },
    {
      name: "Dr. Angel Alcala",
      field: "Marine Biology",
      achievement: "Father of Marine Science in the Philippines",
      inspiration: "Dedicated life to protecting marine ecosystems",
      quote: "We must protect our seas for future generations."
    }
  ],
  artistic: [
    {
      name: "Lea Salonga",
      field: "Performing Arts",
      achievement: "Tony Award winner, voice of Disney princesses",
      inspiration: "Brought Filipino talent to Broadway and Hollywood",
      quote: "Represent your culture with pride wherever you go."
    },
    {
      name: "BenCab (Benedicto Cabrera)",
      field: "Visual Arts",
      achievement: "National Artist for Visual Arts",
      inspiration: "Celebrated Filipino culture through contemporary art",
      quote: "Art is a reflection of our identity and heritage."
    }
  ],
  social: [
    {
      name: "Gina Lopez",
      field: "Environmental Advocacy",
      achievement: "Environmental champion and social entrepreneur",
      inspiration: "Fought for environmental protection and social justice",
      quote: "We must be the change we want to see in our country."
    },
    {
      name: "Dr. Tony Meloto",
      field: "Social Development",
      achievement: "Founder of Gawad Kalinga",
      inspiration: "Transformed slums into thriving communities",
      quote: "Poverty is not just about lack of money, but lack of opportunity."
    }
  ],
  enterprising: [
    {
      name: "Tony Tan Caktiong",
      field: "Business",
      achievement: "Founder of Jollibee Foods Corporation",
      inspiration: "Built a global Filipino food empire",
      quote: "Success comes from serving others with joy and excellence."
    },
    {
      name: "Mariano Que",
      field: "Pharmacy/Business",
      achievement: "Founder of Mercury Drug",
      inspiration: "Started with one pharmacy, built healthcare empire",
      quote: "Serve your community with integrity and compassion."
    }
  ],
  conventional: [
    {
      name: "Jaime Augusto Zobel de Ayala",
      field: "Finance/Management",
      achievement: "Chairman of Ayala Corporation",
      inspiration: "Led one of the Philippines' oldest conglomerates",
      quote: "Good governance and ethical leadership build lasting institutions."
    },
    {
      name: "Corazon Aquino",
      field: "Public Service",
      achievement: "First female President of the Philippines",
      inspiration: "Led the People Power Revolution",
      quote: "The Filipino is capable of great things when united."
    }
  ]
}

// Philippine universities and scholarship programs
export const PHILIPPINE_EDUCATION = {
  topUniversities: [
    {
      name: "University of the Philippines",
      location: "Multiple campuses",
      strengths: ["Engineering", "Medicine", "Liberal Arts", "Business"],
      scholarships: ["DOST Scholarship", "UP Merit Scholarship"],
      website: "up.edu.ph"
    },
    {
      name: "Ateneo de Manila University",
      location: "Quezon City",
      strengths: ["Business", "Liberal Arts", "Engineering", "Medicine"],
      scholarships: ["Ateneo Scholarship Program", "Academic Excellence Grant"],
      website: "ateneo.edu"
    },
    {
      name: "De La Salle University",
      location: "Manila",
      strengths: ["Business", "Engineering", "Liberal Arts", "Computer Science"],
      scholarships: ["DLSU Merit Scholarship", "Brother Andrew Gonzalez Scholarship"],
      website: "dlsu.edu.ph"
    },
    {
      name: "University of Santo Tomas",
      location: "Manila",
      strengths: ["Medicine", "Pharmacy", "Engineering", "Fine Arts"],
      scholarships: ["UST Academic Scholarship", "Thomasian Merit Award"],
      website: "ust.edu.ph"
    }
  ],
  scholarships: [
    {
      name: "DOST Science and Technology Scholarship",
      provider: "Department of Science and Technology",
      coverage: "Full tuition, monthly allowance, book allowance",
      requirements: ["STEM courses", "High academic performance"],
      website: "dost.gov.ph"
    },
    {
      name: "CHED Merit Scholarship",
      provider: "Commission on Higher Education",
      coverage: "Tuition and other fees",
      requirements: ["Academic excellence", "Financial need"],
      website: "ched.gov.ph"
    },
    {
      name: "SM Foundation Scholarship",
      provider: "SM Foundation",
      coverage: "Tuition, allowances, mentoring",
      requirements: ["Academic merit", "Leadership potential"],
      website: "sm-foundation.org"
    },
    {
      name: "Jollibee Group Foundation Scholarship",
      provider: "Jollibee Group Foundation",
      coverage: "Educational assistance",
      requirements: ["Academic performance", "Community involvement"],
      website: "jollibeegroup.com"
    }
  ]
}

// Filipino cultural values and work preferences
export const FILIPINO_VALUES = {
  kapamilya: {
    title: "Kapamilya (Family-oriented)",
    description: "Strong family bonds and support system",
    careerImplication: "Prefer careers that allow work-life balance and family time",
    examples: ["Flexible work arrangements", "Family business", "Local opportunities"]
  },
  bayanihan: {
    title: "Bayanihan (Community spirit)",
    description: "Helping others and working together",
    careerImplication: "Drawn to careers that serve the community",
    examples: ["Teaching", "Healthcare", "Social work", "Public service"]
  },
  pakikipagkapwa: {
    title: "Pakikipagkapwa (Shared identity)",
    description: "Empathy and connection with others",
    careerImplication: "Excel in people-oriented careers",
    examples: ["Counseling", "Human resources", "Customer service", "Leadership"]
  },
  utangNaLoob: {
    title: "Utang na Loob (Gratitude)",
    description: "Recognizing and repaying kindness",
    careerImplication: "Motivated to give back to community and country",
    examples: ["Scholarship programs", "Mentoring", "Community development"]
  }
}

// Career opportunities in the Philippines
export const PHILIPPINE_CAREERS = {
  emerging: [
    "Digital Marketing Specialist",
    "Data Scientist",
    "Cybersecurity Analyst",
    "UX/UI Designer",
    "Content Creator",
    "E-commerce Manager",
    "Renewable Energy Engineer",
    "Telemedicine Specialist"
  ],
  traditional: [
    "Teacher",
    "Nurse",
    "Engineer",
    "Doctor",
    "Lawyer",
    "Accountant",
    "Business Manager",
    "Government Employee"
  ],
  entrepreneurial: [
    "Food Business Owner",
    "Online Seller",
    "Tech Startup Founder",
    "Social Enterprise Leader",
    "Franchise Owner",
    "Consulting Services",
    "Creative Services",
    "Tourism Business"
  ]
}

// Inspirational quotes from Filipino leaders
export const FILIPINO_QUOTES = [
  {
    quote: "The youth is the hope of our future.",
    author: "Dr. José Rizal",
    context: "National Hero"
  },
  {
    quote: "It is better to die on one's feet than to live on one's knees.",
    author: "Dr. José Rizal",
    context: "On courage and dignity"
  },
  {
    quote: "The Filipino is capable of great things when united.",
    author: "Corazon Aquino",
    context: "Former President"
  },
  {
    quote: "Education is the most powerful weapon which you can use to change the world.",
    author: "Nelson Mandela",
    context: "Adopted by Filipino educators"
  },
  {
    quote: "Success is not just about what you accomplish in your life, it's about what you inspire others to do.",
    author: "Manny Pacquiao",
    context: "Boxing champion and public servant"
  }
]

export default {
  MOTIVATIONAL_MESSAGES,
  FILIPINO_ROLE_MODELS,
  PHILIPPINE_EDUCATION,
  FILIPINO_VALUES,
  PHILIPPINE_CAREERS,
  FILIPINO_QUOTES
}
