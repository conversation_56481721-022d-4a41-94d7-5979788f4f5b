import { useState } from 'react'
import Navbar from './components/Navbar'
import HomePage from './components/HomePage'
import Questionnaire from './components/Questionnaire'
import Results from './components/Results'

function App() {
  const [currentPage, setCurrentPage] = useState('home')
  const [responses, setResponses] = useState([])
  const [questionnaireProgress, setQuestionnaireProgress] = useState({ current: 0, total: 0 })
  const [results, setResults] = useState(null)

  const navigateTo = (page) => {
    setCurrentPage(page)
  }

  const startQuestionnaire = () => {
    setResponses([])
    setResults(null)
    setCurrentPage('questionnaire')
  }

  const completeQuestionnaire = (questionnaireResponses) => {
    setResponses(questionnaireResponses)
    // Calculate results here (will implement in next step)
    setCurrentPage('results')
  }

  const goHome = () => {
    setCurrentPage('home')
    setResponses([])
    setResults(null)
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Navbar
        onHomeClick={goHome}
        currentQuestion={questionnaireProgress.current}
        totalQuestions={questionnaireProgress.total}
        showProgress={currentPage === 'questionnaire'}
      />

      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          {currentPage === 'home' && (
            <HomePage onStartQuestionnaire={startQuestionnaire} />
          )}

          {currentPage === 'questionnaire' && (
            <Questionnaire
              onComplete={completeQuestionnaire}
              onBack={goHome}
              onProgressUpdate={setQuestionnaireProgress}
            />
          )}

          {currentPage === 'results' && (
            <Results
              responses={responses}
              onRetake={startQuestionnaire}
              onHome={goHome}
            />
          )}
        </div>
      </main>

      <footer className="bg-blue-900 text-white py-6 mt-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-6 text-center md:text-left">
            <div>
              <h3 className="font-semibold text-blue-200 mb-2">SCOPE Assessment</h3>
              <p className="text-blue-300 text-sm">
                Helping students discover their ideal career path through RIASEC personality assessment
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-blue-200 mb-2">About RIASEC</h3>
              <p className="text-blue-300 text-sm">
                Based on Holland's theory of career choice and personality types
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-blue-200 mb-2">Get Started</h3>
              <p className="text-blue-300 text-sm">
                Take our free 30-question assessment and discover your perfect career match
              </p>
            </div>
          </div>
          <div className="border-t border-blue-800 mt-6 pt-4 text-center">
            <p className="text-blue-400 text-sm">
              © 2024 SCOPE Career Assessment System. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
