import { useState } from 'react'
import Navbar from './components/Navbar'
import HomePage from './components/HomePage'
import Questionnaire from './components/Questionnaire'
import Results from './components/Results'

function App() {
  const [currentPage, setCurrentPage] = useState('home')
  const [responses, setResponses] = useState([])
  const [results, setResults] = useState(null)

  const navigateTo = (page) => {
    setCurrentPage(page)
  }

  const startQuestionnaire = () => {
    setResponses([])
    setResults(null)
    setCurrentPage('questionnaire')
  }

  const completeQuestionnaire = (questionnaireResponses) => {
    setResponses(questionnaireResponses)
    // Calculate results here (will implement in next step)
    setCurrentPage('results')
  }

  const goHome = () => {
    setCurrentPage('home')
    setResponses([])
    setResults(null)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar onHomeClick={goHome} />

      <main className="container mx-auto px-4 py-8">
        {currentPage === 'home' && (
          <HomePage onStartQuestionnaire={startQuestionnaire} />
        )}

        {currentPage === 'questionnaire' && (
          <Questionnaire
            onComplete={completeQuestionnaire}
            onBack={goHome}
          />
        )}

        {currentPage === 'results' && (
          <Results
            responses={responses}
            onRetake={startQuestionnaire}
            onHome={goHome}
          />
        )}
      </main>
    </div>
  )
}

export default App
