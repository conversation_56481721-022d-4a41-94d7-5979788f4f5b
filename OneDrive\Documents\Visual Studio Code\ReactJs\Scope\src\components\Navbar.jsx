function Navbar({ onHomeClick, currentQuestion, totalQuestions, showProgress = false }) {
  const progress = totalQuestions > 0 ? ((currentQuestion + 1) / totalQuestions) * 100 : 0

  return (
    <nav className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div
            className="flex items-center cursor-pointer group transition-all duration-200 hover:scale-105"
            onClick={onHomeClick}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
                <span className="text-blue-600 font-bold text-sm">K</span>
              </div>
              <div>
                <h1 className="text-xl font-bold group-hover:text-blue-200 transition-colors">KursoKo</h1>
                <span className="text-xs text-blue-200 block leading-none">Career Assessment</span>
              </div>
            </div>
          </div>

          {showProgress && totalQuestions > 0 && (
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-2">
                <span className="text-sm text-blue-200">Progress:</span>
                <div className="w-32 bg-blue-800 rounded-full h-2">
                  <div
                    className="bg-white h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <span className="text-sm text-blue-200 font-medium">
                  {Math.round(progress)}%
                </span>
              </div>
              <div className="md:hidden text-sm text-blue-200 font-medium">
                {currentQuestion + 1}/{totalQuestions}
              </div>
            </div>
          )}

          {!showProgress && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-blue-200 hidden sm:block">
                Discover Your Career Path
              </span>
              <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}

export default Navbar
