function Navbar({ onHomeClick, currentQuestion, totalQuestions, showProgress = false }) {
  const progress = totalQuestions > 0 ? ((currentQuestion + 1) / totalQuestions) * 100 : 0

  return (
    <nav className="navbar-youth bg-gradient-youth text-white shadow-youth-lg relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-10 animate-shimmer"></div>

      <div className="container-navbar relative z-10">
        <div className="flex items-center justify-between h-20">
          <div
            className="flex items-center cursor-pointer group transition-all duration-300 hover:scale-105 interactive-scale"
            onClick={onHomeClick}
          >
            <div className="flex items-center">
              {/* Enhanced Logo */}
              <div className="w-12 h-12 bg-white rounded-youth flex items-center justify-center mr-4 shadow-youth group-hover:shadow-youth-hover transition-all duration-300 group-hover:rotate-12">
                <span className="text-primary-600 font-accent text-lg">K</span>
              </div>
              <div>
                <h1 className="text-2xl font-heading font-bold group-hover:text-accent-yellow-200 transition-colors duration-300">
                  KursoKo
                </h1>
                <span className="text-sm text-primary-100 block leading-none font-primary">
                  Career Assessment 🎓
                </span>
              </div>
            </div>
          </div>

          {showProgress && totalQuestions > 0 && (
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-4">
                <span className="text-sm text-primary-100 font-heading">Progress:</span>
                <div className="w-40 bg-primary-800 rounded-full h-3 shadow-inner">
                  <div
                    className="bg-gradient-to-r from-accent-yellow-300 to-accent-orange-300 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden"
                    style={{ width: `${progress}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer"></div>
                  </div>
                </div>
                <span className="text-sm text-accent-yellow-200 font-heading font-bold bg-primary-800 px-3 py-1 rounded-full">
                  {Math.round(progress)}%
                </span>
              </div>
              <div className="md:hidden text-sm text-accent-yellow-200 font-heading font-bold bg-primary-800 px-3 py-1 rounded-full">
                {currentQuestion + 1}/{totalQuestions}
              </div>
            </div>
          )}

          {!showProgress && (
            <div className="flex items-center space-x-4">
              <span className="text-sm text-primary-100 hidden sm:block font-primary">
                Discover Your Career Path ✨
              </span>
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-accent-yellow-300 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-accent-orange-300 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-accent-pink-300 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}

export default Navbar
