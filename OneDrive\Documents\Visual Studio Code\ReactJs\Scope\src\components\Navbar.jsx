function Navbar({ onHomeClick }) {
  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div 
            className="flex items-center cursor-pointer"
            onClick={onHomeClick}
          >
            <h1 className="text-xl font-bold">SCOPE</h1>
            <span className="ml-2 text-sm text-blue-200">Career Assessment</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-sm text-blue-200">
              Discover Your Career Path
            </span>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
