@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Fredoka+One:wght@400&family=Nunito:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== KURSOKO DESIGN SYSTEM ===== */
/* Youth-Friendly Color Palette for Filipino Users (15-25) */
:root {
  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6; /* Electric Blue - Main Brand */
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors */
  --color-secondary-50: #f5f3ff;
  --color-secondary-100: #ede9fe;
  --color-secondary-200: #ddd6fe;
  --color-secondary-300: #c4b5fd;
  --color-secondary-400: #a78bfa;
  --color-secondary-500: #8b5cf6; /* Vibrant Purple */
  --color-secondary-600: #7c3aed;
  --color-secondary-700: #6d28d9;
  --color-secondary-800: #5b21b6;
  --color-secondary-900: #4c1d95;

  /* Accent Colors */
  --color-accent-orange-50: #fffbeb;
  --color-accent-orange-100: #fef3c7;
  --color-accent-orange-200: #fde68a;
  --color-accent-orange-300: #fcd34d;
  --color-accent-orange-400: #fbbf24;
  --color-accent-orange-500: #f59e0b; /* Energetic Orange */
  --color-accent-orange-600: #d97706;
  --color-accent-orange-700: #b45309;
  --color-accent-orange-800: #92400e;
  --color-accent-orange-900: #78350f;

  --color-accent-green-50: #ecfdf5;
  --color-accent-green-100: #d1fae5;
  --color-accent-green-200: #a7f3d0;
  --color-accent-green-300: #6ee7b7;
  --color-accent-green-400: #34d399;
  --color-accent-green-500: #10b981; /* Fresh Green */
  --color-accent-green-600: #059669;
  --color-accent-green-700: #047857;
  --color-accent-green-800: #065f46;
  --color-accent-green-900: #064e3b;

  --color-accent-pink-50: #fdf2f8;
  --color-accent-pink-100: #fce7f3;
  --color-accent-pink-200: #fbcfe8;
  --color-accent-pink-300: #f9a8d4;
  --color-accent-pink-400: #f472b6; /* Coral Pink */
  --color-accent-pink-500: #ec4899;
  --color-accent-pink-600: #db2777;
  --color-accent-pink-700: #be185d;
  --color-accent-pink-800: #9d174d;
  --color-accent-pink-900: #831843;

  --color-accent-yellow-50: #fefce8;
  --color-accent-yellow-100: #fef9c3;
  --color-accent-yellow-200: #fef08a;
  --color-accent-yellow-300: #fde047; /* Sunny Yellow */
  --color-accent-yellow-400: #facc15;
  --color-accent-yellow-500: #eab308;
  --color-accent-yellow-600: #ca8a04;
  --color-accent-yellow-700: #a16207;
  --color-accent-yellow-800: #854d0e;
  --color-accent-yellow-900: #713f12;

  /* Neutral Colors */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;

  /* Semantic Colors */
  --color-success: var(--color-accent-green-500);
  --color-warning: var(--color-accent-orange-500);
  --color-error: #ef4444;
  --color-info: var(--color-primary-500);

  /* Typography - Optimized for Filipino Youth */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-accent: 'Fredoka One', cursive;
  --font-body: 'Nunito', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  /* Typography Scale - Mobile-First */
  --text-xs: clamp(0.75rem, 2vw, 0.875rem);
  --text-sm: clamp(0.875rem, 2.5vw, 1rem);
  --text-base: clamp(1rem, 3vw, 1.125rem);
  --text-lg: clamp(1.125rem, 3.5vw, 1.25rem);
  --text-xl: clamp(1.25rem, 4vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 5vw, 2rem);
  --text-3xl: clamp(2rem, 6vw, 2.5rem);
  --text-4xl: clamp(2.5rem, 7vw, 3rem);
  --text-5xl: clamp(3rem, 8vw, 4rem);

  /* Line Heights - Optimized for readability */
  --leading-tight: 1.1;
  --leading-snug: 1.2;
  --leading-normal: 1.4;
  --leading-relaxed: 1.6;
  --leading-loose: 1.8;

  /* Letter Spacing */
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;

  /* Spacing Scale */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */

  /* Border Radius */
  --radius-sm: 0.5rem;   /* 8px */
  --radius-md: 0.75rem;  /* 12px */
  --radius-lg: 1rem;     /* 16px */
  --radius-xl: 1.5rem;   /* 24px */
  --radius-2xl: 2rem;    /* 32px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-youth: 0 8px 25px rgba(59, 130, 246, 0.15);
  --shadow-youth-hover: 0 12px 35px rgba(59, 130, 246, 0.25);

  /* Transitions */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== YOUTH-FRIENDLY ANIMATIONS ===== */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse-gentle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes wiggle {
  0%, 7%, 14%, 21%, 28%, 35%, 42%, 49%, 56%, 63%, 70%, 77%, 84%, 91%, 98%, 100% {
    transform: translateX(0);
  }
  3.5%, 10.5%, 17.5%, 24.5%, 31.5%, 38.5%, 45.5%, 52.5%, 59.5%, 66.5%, 73.5%, 80.5%, 87.5%, 94.5% {
    transform: translateX(-2px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes progress-fill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

@keyframes confetti {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes rubber-band {
  0% {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes jello {
  0%, 11.1%, 100% {
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 0.5s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-progress-fill {
  animation: progress-fill 1s ease-out forwards;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-rubber-band {
  animation: rubber-band 1s ease-out;
}

.animate-jello {
  animation: jello 0.9s ease-out;
}

.animate-tada {
  animation: tada 1s ease-out;
}

.animate-swing {
  animation: swing 1s ease-out;
}

/* Interactive micro-animations */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-youth-hover);
}

.hover-glow {
  transition: all var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  transform: scale(1.02);
}

.hover-rotate {
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.click-shrink {
  transition: transform var(--transition-fast);
}

.click-shrink:active {
  transform: scale(0.95);
}

/* Staggered animations */
.stagger-children > * {
  animation-delay: calc(var(--stagger-delay, 100ms) * var(--stagger-index, 0));
}

/* Loading animations */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* Success celebration animation */
.celebrate {
  animation: tada 1s ease-out, heartbeat 2s ease-in-out 1s infinite;
}

/* Error shake animation */
.error-shake {
  animation: wiggle 0.5s ease-in-out;
}

/* Attention-seeking animations */
.attention-pulse {
  animation: pulse-gentle 2s ease-in-out infinite;
}

.attention-bounce {
  animation: bounce-in 0.6s ease-out infinite alternate;
}

/* ===== UTILITY CLASSES ===== */
/* ===== ENHANCED TYPOGRAPHY SYSTEM ===== */
.font-primary {
  font-family: var(--font-primary);
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

.font-heading {
  font-family: var(--font-heading);
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

.font-accent {
  font-family: var(--font-accent);
  font-feature-settings: 'kern' 1;
}

.font-body {
  font-family: var(--font-body);
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

/* Typography Hierarchy - Youth-Friendly */
.text-display {
  font-size: var(--text-5xl);
  line-height: var(--leading-tight);
  font-weight: 900;
  letter-spacing: var(--tracking-tight);
  font-family: var(--font-heading);
}

.text-hero {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
  font-weight: 800;
  letter-spacing: var(--tracking-tight);
  font-family: var(--font-heading);
}

.text-title {
  font-size: var(--text-3xl);
  line-height: var(--leading-snug);
  font-weight: 700;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-heading);
}

.text-subtitle {
  font-size: var(--text-xl);
  line-height: var(--leading-normal);
  font-weight: 600;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-heading);
}

.text-heading {
  font-size: var(--text-lg);
  line-height: var(--leading-normal);
  font-weight: 600;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-heading);
}

.text-body-lg {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  font-weight: 400;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-body);
}

.text-body {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  font-weight: 400;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-body);
}

.text-body-sm {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  font-weight: 400;
  letter-spacing: var(--tracking-normal);
  font-family: var(--font-body);
}

.text-caption {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
  font-weight: 500;
  letter-spacing: var(--tracking-wide);
  font-family: var(--font-primary);
  text-transform: uppercase;
}

/* Filipino-Optimized Text Styles */
.text-filipino {
  font-family: var(--font-body);
  line-height: var(--leading-relaxed);
  word-spacing: 0.1em;
}

.text-emphasis {
  font-weight: 600;
  color: var(--color-primary-600);
}

.text-highlight {
  background: linear-gradient(120deg, var(--color-accent-yellow-200) 0%, var(--color-accent-yellow-200) 100%);
  background-repeat: no-repeat;
  background-size: 100% 0.3em;
  background-position: 0 88%;
  padding: 0 0.1em;
}

/* Reading Experience Enhancements */
.text-readable {
  max-width: 65ch;
  line-height: var(--leading-relaxed);
  font-family: var(--font-body);
}

.text-scannable {
  line-height: var(--leading-loose);
  word-spacing: 0.05em;
  font-family: var(--font-primary);
}

/* Interactive Text States */
.text-interactive {
  transition: color var(--transition-normal);
  cursor: pointer;
}

.text-interactive:hover {
  color: var(--color-primary-600);
}

.text-interactive:active {
  color: var(--color-primary-700);
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-warm {
  background: linear-gradient(135deg, var(--color-accent-orange-500), var(--color-accent-pink-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-youth {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
}

.bg-gradient-warm {
  background: linear-gradient(135deg, var(--color-accent-orange-400), var(--color-accent-pink-400));
}

.bg-gradient-success {
  background: linear-gradient(135deg, var(--color-accent-green-400), var(--color-accent-green-600));
}

.shadow-youth {
  box-shadow: var(--shadow-youth);
}

.shadow-youth-hover {
  box-shadow: var(--shadow-youth-hover);
}

.rounded-youth {
  border-radius: var(--radius-lg);
}

.rounded-youth-xl {
  border-radius: var(--radius-xl);
}

.transition-youth {
  transition: all var(--transition-normal);
}

.transition-bounce {
  transition: all var(--transition-bounce);
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interactive-scale {
  transition: transform var(--transition-fast);
}

.interactive-scale:hover {
  transform: scale(1.02);
}

.interactive-scale:active {
  transform: scale(0.98);
}

/* Mobile-first responsive utilities */
.container-youth {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.container-youth-full {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
  min-height: 100vh;
}

.container-navbar {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
  height: auto;
  min-height: auto;
}

@media (min-width: 640px) {
  .container-youth {
    padding: 0 var(--space-lg);
  }

  .container-youth-full {
    padding: 0 var(--space-lg);
  }

  .container-navbar {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .container-youth {
    padding: 0 var(--space-xl);
  }

  .container-youth-full {
    padding: 0 var(--space-xl);
  }

  .container-navbar {
    padding: 0 var(--space-xl);
  }
}

/* Navbar specific styles */
.navbar-youth {
  height: auto;
  min-height: 80px;
  max-height: 120px;
  flex-shrink: 0;
}

.navbar-youth .container-navbar {
  height: 100%;
  display: flex;
  align-items: center;
}

/* Enhanced mobile touch targets */
.touch-target-lg {
  min-height: 56px;
  min-width: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-specific spacing */
.mobile-spacing {
  padding: var(--space-lg) var(--space-md);
}

@media (min-width: 640px) {
  .mobile-spacing {
    padding: var(--space-xl) var(--space-lg);
  }
}

@media (min-width: 1024px) {
  .mobile-spacing {
    padding: var(--space-2xl) var(--space-xl);
  }
}

/* Mobile-optimized text sizes */
.text-mobile-hero {
  font-size: clamp(1.75rem, 8vw, 3.5rem);
  line-height: 1.1;
  font-weight: 800;
}

.text-mobile-title {
  font-size: clamp(1.25rem, 6vw, 2rem);
  line-height: 1.2;
  font-weight: 700;
}

.text-mobile-body {
  font-size: clamp(0.875rem, 4vw, 1.125rem);
  line-height: 1.5;
}

/* Mobile-first grid layouts */
.grid-mobile-stack {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-lg);
}

@media (min-width: 640px) {
  .grid-mobile-stack {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-mobile-stack {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile-optimized buttons */
.btn-mobile {
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  font-size: 1.125rem;
  border-radius: var(--radius-lg);
  min-height: 56px;
}

@media (min-width: 640px) {
  .btn-mobile {
    width: auto;
    min-width: 200px;
  }
}

/* Safe area handling for mobile devices */
.safe-area-padding {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-bounce-in,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-pulse-gentle,
  .animate-wiggle,
  .animate-float,
  .animate-glow,
  .animate-progress-fill {
    animation: none;
  }
}

/* Mobile viewport optimizations */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }

  .mobile-center {
    text-align: center;
  }

  .mobile-stack {
    flex-direction: column;
  }

  .mobile-hide {
    display: none;
  }
}

/* ===== ACCESSIBILITY FEATURES ===== */
/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links for keyboard navigation */
.skip-links {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-600);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: 600;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Keyboard navigation indicators */
.keyboard-navigation *:focus {
  outline: 3px solid var(--color-primary-500);
  outline-offset: 2px;
}

.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus {
  box-shadow: 0 0 0 3px var(--color-primary-500);
}

/* High contrast mode */
.high-contrast {
  --color-primary-500: #000000;
  --color-secondary-500: #000000;
  --color-neutral-800: #000000;
  --color-neutral-600: #000000;
  --color-neutral-400: #666666;
  --color-neutral-200: #cccccc;
  --color-neutral-100: #eeeeee;
  --color-neutral-50: #ffffff;
}

.high-contrast .card-youth {
  border: 2px solid #000000;
}

.high-contrast .btn-primary {
  background: #000000;
  border: 2px solid #000000;
  color: #ffffff;
}

.high-contrast .btn-secondary {
  background: #ffffff;
  border: 2px solid #000000;
  color: #000000;
}

/* Large text mode */
.large-text {
  font-size: 120%;
}

.large-text .text-xs { font-size: 1rem; }
.large-text .text-sm { font-size: 1.125rem; }
.large-text .text-base { font-size: 1.25rem; }
.large-text .text-lg { font-size: 1.5rem; }
.large-text .text-xl { font-size: 1.75rem; }
.large-text .text-2xl { font-size: 2.25rem; }
.large-text .text-3xl { font-size: 3rem; }

/* Reduced motion mode */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Screen reader mode optimizations */
.screen-reader-mode .animate-bounce,
.screen-reader-mode .animate-pulse,
.screen-reader-mode .animate-spin {
  animation: none;
}

.screen-reader-mode .hover\\:scale-105:hover,
.screen-reader-mode .hover\\:scale-110:hover {
  transform: none;
}

/* Focus management */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Improved touch targets for accessibility */
.touch-accessible {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Color contrast helpers */
.contrast-aa {
  color: var(--color-neutral-800);
  background: var(--color-neutral-50);
}

.contrast-aaa {
  color: var(--color-neutral-900);
  background: var(--color-neutral-50);
}

/* Error states for accessibility */
.error-state {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.error-state:focus {
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.3);
}

/* Success states for accessibility */
.success-state {
  border-color: var(--color-accent-green-500);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.success-state:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
}

/* Loading states for accessibility */
.loading-state {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading-state::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-primary-200);
  border-top-color: var(--color-primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Print styles for accessibility */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }
}

/* ===== COMPONENT STYLES ===== */
/* Youth-friendly buttons */
.btn-primary {
  background: var(--bg-gradient-youth);
  color: white;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-youth);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-youth-hover);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: white;
  color: var(--color-primary-600);
  border: 2px solid var(--color-primary-500);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.btn-secondary:hover {
  background: var(--color-primary-50);
  transform: translateY(-1px);
}

/* Card components */
.card-youth {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-youth);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-neutral-200);
}

.card-youth:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-youth-hover);
}

/* Progress bars */
.progress-bar-youth {
  width: 100%;
  height: 12px;
  background: var(--color-neutral-200);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-bar-youth::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--bg-gradient-youth);
  border-radius: 6px;
  transition: width var(--transition-slow);
}

/* Illustration placeholders */
.illustration-placeholder {
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-secondary-100));
  border: 2px dashed var(--color-primary-300);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.illustration-placeholder:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-youth);
}

.illustration-placeholder::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all var(--transition-slow);
  opacity: 0;
}

.illustration-placeholder:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Rating buttons */
.rating-btn {
  background: white;
  border: 2px solid var(--color-neutral-300);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.rating-btn:hover {
  border-color: var(--color-primary-400);
  background: var(--color-primary-50);
  transform: translateY(-1px);
}

.rating-btn.selected {
  border-color: var(--color-primary-500);
  background: var(--bg-gradient-youth);
  color: white;
  box-shadow: var(--shadow-youth);
}

.rating-btn.selected:hover {
  box-shadow: var(--shadow-youth-hover);
}

/* Responsive text sizes */
.text-hero {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  font-weight: 800;
}

.text-title {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  line-height: 1.2;
  font-weight: 700;
}

.text-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  line-height: 1.4;
  font-weight: 500;
}
