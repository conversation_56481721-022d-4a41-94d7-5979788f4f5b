# Technical Context

## Tech Stack
- Frontend: React.js, Axios
- Backend: Node.js, Express, Nodemailer
- Database: None (optional MongoDB for backups)
- Email Service: SMTP / SendGrid

## Development Setup
- Node 16.x, npm
- Create React App or Vite
- Linting: ESLint, Prettier
- Version control: Git

## Constraints & Decisions
- Anonymous assessments: no user accounts
- Email-based result delivery (no in-app history)
- Mobile-first responsive design
